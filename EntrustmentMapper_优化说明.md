# EntrustmentMapper SQL 优化说明

## 优化概述

本次优化主要针对 `EntrustmentMapper.java` 和 `EntrustmentMapper.xml` 进行了以下改进：

### 1. 移除未使用的SQL查询

#### 已删除的方法：
- `getEntrustmentData` - 接口中定义但没有对应的XML实现，也没有被调用
- `selectPendingApprovalList` - 有XML实现但参数不匹配，且未被使用

### 2. 合并传参一致的SQL

#### 原有重复方法：
- `selectUnapprovedEntrustments` 和 `getUnapprovedEntrustments` - 功能完全相同
- `getNoCommitData` 和 `selectFirstByReportNoAndCaseTimes` - 查询条件相似
- `selectByReportNoAndCaseTime` - 功能可以被通用方法替代

#### 新的统一方法：
```java
List<EntrustMainDTO> selectByConditions(
    @Param("reportNo") String reportNo, 
    @Param("caseTimes") Integer caseTimes, 
    @Param("idEntrust") String idEntrust,
    @Param("entrustStatus") String entrustStatus,
    @Param("orderBy") String orderBy,
    @Param("limitOne") Boolean limitOne
);
```

### 3. 固定查询条件移到Java代码

#### 优化前：
- `selectForPrint` 在SQL中硬编码了 `THIRD_PARTY_TYPE = '01'` 和 `ENTRUST_STATUS = '3'`
- `getUnapprovedEntrustments` 在SQL中硬编码了 `ENTRUST_STATUS IN ('0', '1')`

#### 优化后：
- 在Service层添加了 `getEntrustmentsForPrintFiltered()` 方法进行过滤
- 在Service层添加了 `getUnapprovedEntrustments()` 方法进行状态过滤
- SQL查询更加通用，条件判断在Java代码中进行

### 4. Service层方法更新

#### 更新的方法：
1. `getCurrentEntrustment()` - 使用新的通用查询方法
2. `getCurrentDraftOrPendingEntrustment()` - 使用状态过滤查询草稿数据
3. `getFirstEntrustmentByReportNo()` - 使用升序排序获取第一条记录
4. `getEntrustmentForPrint()` - 使用新的过滤方法

#### 新增的辅助方法：
1. `getUnapprovedEntrustments()` - 查询未审批完成的委托任务
2. `getEntrustmentsForPrintFiltered()` - 查询并过滤打印委托信息

### 5. XML优化详情

#### 删除的SQL片段：
```xml
<!-- 已删除 -->
<select id="selectUnapprovedEntrustments">...</select>
<select id="getUnapprovedEntrustments">...</select>
<select id="getNoCommitData">...</select>
<select id="selectFirstByReportNoAndCaseTimes">...</select>
<select id="selectByReportNoAndCaseTime">...</select>
<select id="selectPendingApprovalList">...</select>
```

#### 新增的通用SQL：
```xml
<select id="selectByConditions" resultMap="result">
    SELECT * FROM CLMS_ENTRUS_MAIN
    WHERE REPORT_NO = #{reportNo} 
    <if test="caseTimes != null">
        AND CASE_TIMES = #{caseTimes}
    </if>
    <if test="idEntrust != null and idEntrust != ''">
        AND ID_ENTRUST = #{idEntrust}
    </if>
    <if test="entrustStatus != null and entrustStatus != ''">
        AND ENTRUST_STATUS IN 
        <foreach collection="entrustStatus.split(',')" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
    </if>
    AND VALID_FLAG = 'Y'
    <choose>
        <when test="orderBy == 'ASC'">
            ORDER BY SYS_CTIME ASC
        </when>
        <otherwise>
            ORDER BY SYS_CTIME DESC
        </otherwise>
    </choose>
    <if test="limitOne != null and limitOne == true">
        LIMIT 1
    </if>
</select>
```

### 6. 优化效果

1. **代码简化**：从12个查询方法减少到7个方法
2. **维护性提升**：通用查询方法减少了重复代码
3. **灵活性增强**：固定条件移到Java代码中，便于后续调整
4. **性能优化**：减少了不必要的SQL查询
5. **可读性提升**：方法职责更加清晰

### 7. 兼容性说明

- 所有原有的Service接口方法保持不变
- 对外API接口无任何变化
- 业务逻辑完全兼容原有实现

## 使用示例

```java
// 查询草稿状态的委托
List<EntrustMainDTO> drafts = entrustmentMapper.selectByConditions(
    reportNo, caseTimes, null, "0", "DESC", false);

// 查询第一条委托记录
List<EntrustMainDTO> first = entrustmentMapper.selectByConditions(
    reportNo, caseTimes, null, null, "ASC", true);

// 查询未审批完成的委托
List<EntrustMainDTO> unapproved = entrustmentMapper.selectByConditions(
    reportNo, caseTimes, null, "0,1", "DESC", false);
```
