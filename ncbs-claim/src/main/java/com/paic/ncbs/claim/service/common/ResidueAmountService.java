package com.paic.ncbs.claim.service.common;

import com.paic.ncbs.claim.model.dto.settle.HistoryPayInfoDTO;

import java.math.BigDecimal;

/**
 * 查询剩余理赔额服务接口
 */
public interface ResidueAmountService {
    /**
     * 责任明细层级
     * @param scene
     * @param reportNo
     * @param policyNo
     * @param planCode
     * @param dutyCode
     * @param dutyDetailCode
     * @param isShareAmount
     * @return
     */
    BigDecimal getDutyDetailHistoryPay(String scene, String reportNo, String policyNo,
                                     String planCode, String dutyCode, String dutyDetailCode, Boolean isShareAmount);
    /**
     * 计算“责任明细“的剩余可赔付额
     */
    BigDecimal getDutyDetailMaxPay(HistoryPayInfoDTO dutyDetailHistoryPay);
    /**
     * 查询责任历史赔付金额
     */
    BigDecimal getDutyHistoryPay(String scene, String reportNo, String policyNo, String planCode,
                                        String dutyCode, Boolean isDutyShareAmount, String shareDutyGroup, Boolean isShareAmount);

    BigDecimal getDutyHistoryPay(String policyNo, String planCode, String dutyCode, Boolean isDutyShareAmount,
                                 String shareDutyGroup, Boolean isShareAmount,String clientNo);

    /**
     * 计算“责任”的剩余可赔付额
     */
    BigDecimal getDutyMaxPay(HistoryPayInfoDTO dutyHistoryPay);

    BigDecimal getPolicyHistoryPay(String reportNo, String policyNo);

    BigDecimal getNowCaseDutyPrePay(String caseNo, String policyNo, String planCode,String dutyCode, Integer caseTimes);
}
