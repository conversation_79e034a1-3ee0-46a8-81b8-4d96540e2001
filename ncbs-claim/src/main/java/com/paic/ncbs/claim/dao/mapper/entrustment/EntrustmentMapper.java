package com.paic.ncbs.claim.dao.mapper.entrustment;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustMainDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;
import java.util.Map;

@MapperScan
public interface EntrustmentMapper extends BaseDao {

    void insertEntrustment(EntrustMainDTO entrustMain);

    void updateEntrustment(EntrustMainDTO entrustMain);

    void deleteEntrustmentNoOperate(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("idEntrust") String idEntrust);

    /**
     * 根据报案号和赔付次数查询委托记录（支持按状态过滤）
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @param idEntrust 委托ID（可选）
     * @param entrustStatus 委托状态（可选，多个状态用逗号分隔）
     * @param orderBy 排序方式：ASC-升序，DESC-降序
     * @param limitOne 是否只返回一条记录
     * @return 委托DTO或列表
     */
    List<EntrustMainDTO> selectByConditions(@Param("reportNo") String reportNo,
                                           @Param("caseTimes") Integer caseTimes,
                                           @Param("idEntrust") String idEntrust,
                                           @Param("entrustStatus") String entrustStatus,
                                           @Param("orderBy") String orderBy,
                                           @Param("limitOne") Boolean limitOne);

    List<EntrustMainDTO> selectHistoryByReportNo(@Param("reportNo") String reportNo);

    EntrustMainDTO selectById(@Param("idEntrust") String idEntrust);

    List<EntrustMainDTO> selectForPrint(@Param("reportNo") String reportNo);

    /**
     * 根据报案号查询保单号和被保险人姓名
     * @param reportNo 报案号
     * @return 包含policyNo和insuredName的Map
     */
    Map<String, String> getPolicyInfoByReportNo(@Param("reportNo") String reportNo);

    /**
     * 根据报案号查询案件机构代码
     * @param reportNo 报案号
     * @return 案件机构代码
     */
    String getDepartmentCodeByReportNo(@Param("reportNo") String reportNo);
}