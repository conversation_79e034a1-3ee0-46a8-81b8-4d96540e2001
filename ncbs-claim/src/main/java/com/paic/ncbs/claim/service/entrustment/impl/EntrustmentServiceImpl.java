package com.paic.ncbs.claim.service.entrustment.impl;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.InvestigateConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.user.DepartmentDefineEntity;
import com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentAuditMapper;
import com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustAuditDTO;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustMainDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO;
import com.paic.ncbs.claim.model.dto.print.PrintEntrustDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.entrustment.EntrustmentApiVo;
import com.paic.ncbs.claim.model.vo.entrustment.ApprovalUserVO;
import com.paic.ncbs.claim.model.vo.investigate.ServerInfoVO;
import com.paic.ncbs.claim.model.vo.investigate.TpaServerInfoListVO;
import com.paic.ncbs.claim.model.vo.taskdeal.TaskInfoVO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.doc.PrintCoreService;
import com.paic.ncbs.claim.service.entrustment.EntrustmentService;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.notice.NoticeService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("entrustmentService")
public class EntrustmentServiceImpl implements EntrustmentService {

    @Autowired
    private EntrustmentMapper entrustmentMapper;
    @Autowired
    private EntrustmentAuditMapper entrustmentAuditMapper;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private BpmService bpmService;
    @Autowired
    private IOperationRecordService operationRecordService;
    @Autowired
    InvestigateMapper investigateMapper;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private InvestigateService investigateService;
    @Autowired
    private DepartmentDefineMapper departmentDefineMapper ;
    @Autowired
    private TaskPoolService taskPoolService;
    @Autowired
    private PrintCoreService printCoreService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Object> saveEntrustment(EntrustmentApiVo entrustmentApiVo) throws GlobalBusinessException {
        LogUtil.info("开始保存第三方委托" + JsonUtils.toJsonString(entrustmentApiVo));
        UserInfoDTO u = WebServletContext.getUser();
        EntrustMainDTO entrustMainDTO = entrustmentApiVo.getEntrustMainDTO();
        // 检查是否可以发起委托
        checkIsCanSendEntrustment(entrustmentApiVo);

        String entrustId = UuidUtil.getUUID();

        // 查询第一条委托记录（按创建时间升序）
        List<EntrustMainDTO> firstEntrustmentList = entrustmentMapper.selectByConditions(
            entrustMainDTO.getReportNo(), entrustMainDTO.getCaseTimes(), null, null, "ASC", true);
        EntrustMainDTO existingEntrustment = firstEntrustmentList.isEmpty() ? null : firstEntrustmentList.get(0);
        
        if (existingEntrustment != null) {
            String entrustStatus = existingEntrustment.getEntrustStatus();
            if ("0".equals(entrustStatus)) {
                entrustMainDTO.setIdEntrust(existingEntrustment.getIdEntrust());
                entrustMainDTO.setEntrustStatus(entrustmentApiVo.getSubmitFlag()); // 0-草稿、1-待审批、2-不同意、3-同意
                entrustMainDTO.setSysUtime(new Date()); // 更新修改时间
                entrustmentMapper.updateEntrustment(entrustMainDTO);
            } else if ("2".equals(entrustStatus) || "3".equals(entrustStatus)) {
                entrustMainDTO.setIdEntrust(entrustId);
                entrustMainDTO.setEntrustStatus(entrustmentApiVo.getSubmitFlag()); // 0-草稿、1-待审批、2-不同意、3-同意
                entrustMainDTO.setPrintStatus("2");// 打印状态 1-已完成 2-未完成
                entrustMainDTO.setValidFlag("Y");
                entrustMainDTO.setCreatedBy(u.getUserCode());
                entrustMainDTO.setUpdatedBy(u.getUserCode());
                entrustMainDTO.setSysCtime(new Date());
                entrustMainDTO.setSysUtime(new Date());
                entrustmentMapper.insertEntrustment(entrustMainDTO);
            } else if ("1".equals(entrustStatus)) {
                throw new GlobalBusinessException("有正在审批中的委托，请不要重复提交");
            }
        } else {
            entrustMainDTO.setIdEntrust(entrustId);
            entrustMainDTO.setEntrustStatus(entrustmentApiVo.getSubmitFlag()); // 0-草稿、1-待审批、2-不同意、3-同意
            entrustMainDTO.setPrintStatus("2");// 打印状态 1-已完成 2-未完成
            entrustMainDTO.setValidFlag("Y");
            entrustMainDTO.setCreatedBy(u.getUserCode());
            entrustMainDTO.setUpdatedBy(u.getUserCode());
            entrustMainDTO.setSysCtime(new Date());
            entrustMainDTO.setSysUtime(new Date());
            entrustmentMapper.insertEntrustment(entrustMainDTO);
        }

        if("1".equals(entrustmentApiVo.getSubmitFlag())){
            // 当为提交操作时，生成一笔审批任务
            String entrustAuditId = generateEntrustmentAuditTask(entrustMainDTO, u);
            // 启动审批流程
            bpmService.startProcessEntrustment(entrustMainDTO, BpmConstants.OC_ENTRUSTMENT_APPROVAL,entrustAuditId,u);
            // 记录操作日志
            operationRecordService.insertOperationRecord(entrustMainDTO.getReportNo(), BpmConstants.OC_ENTRUSTMENT_APPROVAL, "发起", null, u.getUserCode());
        }
        return ResponseResult.success();
    }

    @Override
    public void checkIsCanSendEntrustment(EntrustmentApiVo apiVo) throws GlobalBusinessException {
        if (!StringUtils.hasText(apiVo.getReportNo())) {
            throw new GlobalBusinessException("报案号不能为空");
        }
        if (apiVo.getCaseTimes() == null) {
            throw new GlobalBusinessException("赔付次数不能为空");
        }
    }

    @Override
    public List<AccidentSceneDto> getAccidentSceneData(String collectionCode) {
        List<AccidentSceneDto> list = new ArrayList<>();
        String[] codes = collectionCode.split(",");
        for (String code : codes) {
            List<AccidentSceneDto> temp = investigateMapper.getAccidentSceneData(code);
            list.addAll(temp);
        }
        //剔除 意外事故、疾病医疗、重大疾病、身故
        list.removeIf(item -> "ASM_1001".equals(item.getValueCode())
                || "ASM_1002".equals(item.getValueCode())
                || "ASM_1003".equals(item.getValueCode())
                || "ASM_1004".equals(item.getValueCode()));
        return list;
    }

    @Override
    public Integer getEntrustmentCount(String reportNo, Integer caseTimes) {
        // 实现获取委托次数的逻辑
        List<EntrustMainDTO> list = entrustmentMapper.selectHistoryByReportNo(reportNo);
        return list != null ? list.size() : 0;
    }

    @Override
    public EntrustMainDTO getCurrentEntrustment(String reportNo, Integer caseTimes, String idEntrust) {
        List<EntrustMainDTO> results = entrustmentMapper.selectByConditions(reportNo, caseTimes, idEntrust, null, "DESC", true);
        return results.isEmpty() ? null : results.get(0);
    }

    @Override
    public ResponseResult<Object> getEntrustmentForPrint(String reportNo) {
        // 使用私有方法获取已过滤的委托列表
        List<EntrustMainDTO> filteredList = getEntrustmentsForPrintFiltered(reportNo);

        // 关联任务完成时间
        TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
        taskInfoDTO.setReportNo(reportNo);
        List<TaskInfoVO> taskInfoVOList = taskInfoMapper.getEntrustmentTaskInfo(taskInfoDTO);
        Map<String, TaskInfoVO> mapA = new HashMap<>();
        for (TaskInfoVO taskInfoVO : taskInfoVOList) {
            mapA.put(taskInfoVO.getOrderNo(), taskInfoVO);
        }
        for (EntrustMainDTO dto : filteredList) {
            TaskInfoVO vo = mapA.get(dto.getIdEntrust());
            if (vo != null && vo.getCompleteTime() != null) {
                dto.setCompleteTime(vo.getCompleteTime());
            }
        }

        // 增加公估公司
        TpaServerInfoListVO tpaServerInfoList = (TpaServerInfoListVO) investigateService.getServerInfoList().getData();
        if (tpaServerInfoList != null && tpaServerInfoList.getServerInfoList() != null) {
            List<ServerInfoVO> serverInfoList = tpaServerInfoList.getServerInfoList();
            Map<String, ServerInfoVO> mapB = new HashMap<>();
            for (ServerInfoVO serverInfoVO : serverInfoList) {
                mapB.put(serverInfoVO.getServerCode(), serverInfoVO);
            }
            for (EntrustMainDTO dto : filteredList) {
                ServerInfoVO vo = mapB.get(dto.getEntrustDptCode());
                if (vo != null) {
                    dto.setEntrustDptName(vo.getServerName());
                }
            }
        }

        // 增加序号
        List<EntrustMainDTO> list2 = new ArrayList<>();
        int i = 1;
        int j = 0;
        for (EntrustMainDTO EntrustMainDTO : filteredList) {
            EntrustMainDTO.setOrderNo(i + "-" + ++j);
            list2.add(EntrustMainDTO);
        }

        // 按完成时间倒序排列
        list2.sort((p1, p2) -> {
            if (p1.getCompleteTime() == null && p2.getCompleteTime() == null) {
                return 0;
            }
            if (p1.getCompleteTime() == null) {
                return 1;
            }
            if (p2.getCompleteTime() == null) {
                return -1;
            }
            return p2.getCompleteTime().compareTo(p1.getCompleteTime());
        });

        EntrustmentApiVo apiVo = new EntrustmentApiVo();
        apiVo.setEntrustMainList(list2);
        return ResponseResult.success(apiVo);
    }


    @Override
    public List<EntrustMainDTO> getHistoryEntrustments(String reportNo, Integer caseTimes, String idEntrustment) {
        // 获取所有历史委托数据
        List<EntrustMainDTO> allHistory = entrustmentMapper.selectHistoryByReportNo(reportNo);

        // 过滤掉当前案件
        return allHistory.stream()
                .filter(dto -> !dto.getIdEntrust().equals(idEntrustment))
                .collect(Collectors.toList());
    }

    /**
     * 生成委托审批任务
     * @param user 当前用户
     */
    private String generateEntrustmentAuditTask(EntrustMainDTO entrustMainDTO, UserInfoDTO user) {
        // 创建审批记录
        EntrustAuditDTO auditDTO = new EntrustAuditDTO();
        auditDTO.setIdEntrustAudit(UuidUtil.getUUID());
        auditDTO.setIdEntrustMain(entrustMainDTO.getIdEntrust());
        auditDTO.setReportNo(entrustMainDTO.getReportNo());
        auditDTO.setCaseTimes(entrustMainDTO.getCaseTimes());

        // 从查询结果中获取保单号和被保险人姓名
        Map<String, String> policyInfo = entrustmentMapper.getPolicyInfoByReportNo(entrustMainDTO.getReportNo());
        if (policyInfo != null) {
            auditDTO.setPolicyNo(policyInfo.get("policyNo"));
            auditDTO.setInsuredName(policyInfo.get("insuredName"));
        }

        // 复制委托信息中的数据
        auditDTO.setThirdPartyType(entrustMainDTO.getThirdPartyType());
        auditDTO.setEntrustDptCode(entrustMainDTO.getEntrustDptCode());
        auditDTO.setEntrustDptName(entrustMainDTO.getEntrustDptName());
        auditDTO.setEntrustmentDpmCode(entrustMainDTO.getEntrustDptCode());
        auditDTO.setEntrustmentDpmName(entrustMainDTO.getEntrustDptName());

        // 设置发起人信息
        auditDTO.setSubmitCode(user.getUserCode());
        auditDTO.setSubmitName(user.getUserName());

        // 设置审批人信息
        auditDTO.setAuditorCode(entrustMainDTO.getAuditorCode());
        auditDTO.setAuditorName(entrustMainDTO.getAuditorUmName());
        auditDTO.setAuditorDptCode(entrustMainDTO.getAuditorDpmCode());
        auditDTO.setAuditorDptName(entrustMainDTO.getAuditorDpmName());

        // 设置基础信息
        auditDTO.setAuditOpinion("1");// 1-待审核 2-不同意、3-同意
        auditDTO.setValidFlag("Y");
        auditDTO.setCreatedBy(user.getUserCode());
        auditDTO.setUpdatedBy(user.getUserCode());
        auditDTO.setSysCtime(new Date());
        auditDTO.setSysUtime(new Date());

        // 查询是否有在审批中的记录，没有就新增
        entrustmentAuditMapper.insertEntrustmentAudit(auditDTO);
        return auditDTO.getIdEntrustAudit();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitentrustAudit(EntrustAuditDTO entrustAuditDTO) throws GlobalBusinessException {
        // 更新审批记录
        EntrustAuditDTO entrustmentAudit = new EntrustAuditDTO();
        entrustmentAudit.setIdEntrustAudit(entrustAuditDTO.getIdEntrustAudit());
        entrustmentAudit.setAuditOpinion(entrustAuditDTO.getAuditOpinion());
        entrustmentAudit.setAuditTime(new Date());
        entrustmentAudit.setRemark(entrustAuditDTO.getRemark());
        entrustmentAudit.setSysUtime(new Date());
        // 当前审批人
        UserInfoDTO user = WebServletContext.getUser();
        entrustmentAudit.setAuditorCode(user.getUserCode());
        entrustmentAudit.setAuditorName(user.getUserName());
        entrustmentAudit.setAuditorDptCode(WebServletContext.getDepartmentCode());
        entrustmentAudit.setAuditorDptName(departmentDefineMapper.queryDepartmentNameByDeptCode(WebServletContext.getDepartmentCode()));
        entrustmentAudit.setAuditTime(new Date());
        entrustmentAuditMapper.updateEntrustmentAudit(entrustmentAudit);

        // 更新委托表状态
        EntrustMainDTO entrustment = entrustmentMapper.selectById(entrustAuditDTO.getIdEntrustMain());
        if (entrustment != null) {
            if ("2".equals(entrustmentAudit.getAuditOpinion())) { // 不同意
                entrustment.setEntrustStatus("2");

                // 生成提醒任务
                generateRejectionNotice(entrustment, entrustment.getCreatedBy(), user.getUserName());
            } else if ("3".equals(entrustmentAudit.getAuditOpinion())) { // 同意
                entrustment.setEntrustStatus("3");

                //生成公估委托书
                PrintEntrustDTO printEntrustDTO = new PrintEntrustDTO();
                printEntrustDTO.setIdEntrust(entrustAuditDTO.getIdEntrustMain());
                printEntrustDTO.setPrintFlag(InvestigateConstants.PRINT_ENTRUSTMENT);
                String now = System.currentTimeMillis()+"";
                printCoreService.saveCommissionFileAsync(now,null,now,printEntrustDTO);
            }

            entrustment.setSysUtime(new Date());
            entrustment.setUpdatedBy(user.getUserCode());
            entrustmentMapper.updateEntrustment(entrustment);
        }
        // 关闭OC流程
        bpmService.completeTask_oc(entrustment.getReportNo(), entrustment.getCaseTimes(), BpmConstants.OC_ENTRUSTMENT_APPROVAL);

        // 操作记录
        operationRecordService.insertOperationRecordByLabour(entrustment.getReportNo(), BpmConstants.OC_ENTRUSTMENT_APPROVAL, "通过", entrustAuditDTO.getRemark());
    }

    /**
     * 生成审批退回提醒任务
     * @param entrustment 委托信息
     * @param audit 审批信息
     */
    private void generateRejectionNotice(EntrustMainDTO entrustment, String audit, String auditorName) {
        try {
            NoticesDTO noticesDTO = new NoticesDTO();
            noticesDTO.setReportNo(entrustment.getReportNo());
            noticesDTO.setNoticeClass(BpmConstants.NOTICE_CLASS_OC_BACK); // 审批退回
            noticesDTO.setNoticeSubClass(BpmConstants.NSC_APPRAISAL_COMMISSION); // 公估委托审批
            noticesDTO.setSourceSystem(BpmConstants.SOURCE_SYSTEM_OC);
            noticesDTO.setCaseTimes(entrustment.getCaseTimes());

            // 提醒内容："{报案号}第三方委托申请被{审批人}审批退回"
            String noticeContent = entrustment.getReportNo() + "第三方委托申请被" + auditorName + "审批退回";
            noticesDTO.setNoticeContent(noticeContent);

            // 发送给发起人
            noticeService.saveNotices(noticesDTO, audit);
        } catch (Exception e) {
            LogUtil.error("生成委托审批退回提醒失败", e);
        }
    }

    @Override
    public List<ApprovalUserVO> getApprovalUsers(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        try {
            String departmentCode = entrustmentMapper.getDepartmentCodeByReportNo(reportNo);

            List<String> departmentCodes = new ArrayList<>();
            departmentCodes.add(departmentCode);
            DepartmentDefineEntity dep = departmentDefineMapper.getDepartmentInfo(departmentCode);
            if (dep != null && !StringUtils.isEmpty(dep.getUpperDepartmentCode())) {
                departmentCode = dep.getUpperDepartmentCode(); // 获取上级部门
                departmentCodes.add(dep.getUpperDepartmentCode());
            }

//            List<UserInfoDTO> allUsers = taskPoolService.searchTaskDealUser(departmentCode, BpmConstants.OC_INVESTIGATE_APPROVAL);
            String userListString = "[{\"accountCode\":null,\"userCode\":\"mandyhu\",\"userName\":\"胡本曼\",\"comCode\":\"1\",\"comName\":\"三星财险\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"hellenjin\",\"userName\":\"金慧玲\",\"comCode\":\"1\",\"comName\":\"三星财险\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"demo2\",\"userName\":\"演示账户02\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"yongqihe\",\"userName\":\"何咏祺\",\"comCode\":\"915\",\"comName\":\"非车理赔部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"jiayonglu\",\"userName\":\"陆佳勇\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"madelynsun\",\"userName\":\"孙钰琳\",\"comCode\":\"915\",\"comName\":\"非车理赔部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"blueshen\",\"userName\":\"沈文\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"serenazhan\",\"userName\":\"詹烨\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"demo5\",\"userName\":\"演示账户05\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"careyzhu\",\"userName\":\"朱江红\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"chasenzhang\",\"userName\":\"张全志\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"aryali\",\"userName\":\"李莎莎\",\"comCode\":\"1\",\"comName\":\"三星财险\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"jaywei\",\"userName\":\"韦杰\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"v_mtaone\",\"userName\":\"民太安轮岗一\",\"comCode\":\"915\",\"comName\":\"非车理赔部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"2\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"demo10\",\"userName\":\"演示账户10\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"susanxu\",\"userName\":\"徐南海\",\"comCode\":\"873\",\"comName\":\"两核产品中心\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"jiezhang\",\"userName\":\"张洁\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"bingoliu\",\"userName\":\"刘小兵\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"leeli\",\"userName\":\"李俊超\",\"comCode\":\"917\",\"comName\":\"团险营业部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"sherryjiang\",\"userName\":\"江颖颖\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"arthurwchen\",\"userName\":\"陈伟\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"ryanyang\",\"userName\":\"杨斌\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"\",\"phone\":\"\",\"userType\":\"10\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"andyma\",\"userName\":\"马明\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"demo6\",\"userName\":\"演示账户06\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"jeffji\",\"userName\":\"姬凯\",\"comCode\":\"872\",\"comName\":\"团险事业中心\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"ficoxu\",\"userName\":\"徐飞\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"oceanouyang\",\"userName\":\"欧阳远成\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"antzang\",\"userName\":\"臧国臣\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"demo3\",\"userName\":\"演示账户03\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"demo4\",\"userName\":\"演示账户04\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"hanajin\",\"userName\":\"金红莲\",\"comCode\":\"915\",\"comName\":\"非车理赔部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"shengkaisu\",\"userName\":\"苏盛开\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"miralu\",\"userName\":\"卢美玲\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"buseqi\",\"userName\":\"漆冰\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"ericyuan\",\"userName\":\"袁兰\",\"comCode\":\"915\",\"comName\":\"非车理赔部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"\",\"phone\":\"021-********\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"demo7\",\"userName\":\"演示账户07\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"demo1\",\"userName\":\"演示账户01\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"hokyhu\",\"userName\":\"胡爱军\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"leoxu\",\"userName\":\"徐翊鹏\",\"comCode\":\"971\",\"comName\":\"意健险部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"demo8\",\"userName\":\"演示账户08\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"demo9\",\"userName\":\"演示账户09\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"},{\"accountCode\":null,\"userCode\":\"v_yangli\",\"userName\":\"李阳\",\"comCode\":\"863\",\"comName\":\"信息技术部\",\"comLevel\":null,\"makeCom\":null,\"makeComName\":null,\"loginSystem\":null,\"mobile\":\"***********\",\"phone\":\"\",\"userType\":\"1\",\"validStatus\":\"0\",\"email\":\"<EMAIL>\"}]";
            List<UserInfoDTO> allUsers = JsonUtils.toObjectArray(userListString, UserInfoDTO.class);
            
            if (allUsers == null || allUsers.isEmpty()) {
                return new ArrayList<>();
            }

            List<UserInfoDTO> filteredUsers = allUsers.stream()
                    .filter(user -> departmentCodes.contains(user.getComCode()))
                    .collect(Collectors.toList());

            List<ApprovalUserVO> result = new ArrayList<>();
            for (UserInfoDTO user : allUsers) {
                ApprovalUserVO vo = new ApprovalUserVO();
                vo.setUserCode(user.getUserCode());
                vo.setUserName(user.getUserName());
                vo.setComCode(user.getComCode());
                vo.setComName(user.getComName());
                result.add(vo);
            }

            return result;
        } catch (Exception e) {
            throw new GlobalBusinessException("获取审批用户列表失败：" + e.getMessage());
        }
    }

    @Override
    public EntrustMainDTO getCurrentDraftOrPendingEntrustment(String reportNo, Integer caseTimes) {
        // 查询草稿状态的委托数据（状态为'0'）
        List<EntrustMainDTO> results = entrustmentMapper.selectByConditions(reportNo, caseTimes, null, "0", "DESC", true);
        return results.isEmpty() ? null : results.get(0);
    }

    @Override
    public List<EntrustAuditDTO> getAuditHistoryByEntrustId(String idEntrust) {
        return entrustmentAuditMapper.selectAuditHistoryByEntrustId(idEntrust);
    }

    @Override
    public List<EntrustAuditDTO> getAuditHistoryByReportNo(String reportNo, Integer caseTimes) {
        return entrustmentAuditMapper.selectAuditHistoryByReportNo(reportNo, caseTimes);
    }

    @Override
    public EntrustMainDTO getFirstEntrustmentByReportNo(String reportNo, Integer caseTimes) {
        // 查询第一条委托记录（按创建时间升序）
        List<EntrustMainDTO> results = entrustmentMapper.selectByConditions(reportNo, caseTimes, null, null, "ASC", true);
        return results.isEmpty() ? null : results.get(0);
    }

    /**
     * 查询未审批完成的委托任务（状态为草稿或待审批）
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 未审批完成的委托任务列表
     */
    public List<EntrustMainDTO> getUnapprovedEntrustments(String reportNo, Integer caseTimes) {
        // 查询状态为'0'（草稿）或'1'（待审批）的委托记录
        return entrustmentMapper.selectByConditions(reportNo, caseTimes, null, "0,1", "DESC", false);
    }

    /**
     * 查询用于打印的委托信息（在Service层进行过滤）
     * @param reportNo 报案号
     * @return 符合打印条件的委托列表
     */
    private List<EntrustMainDTO> getEntrustmentsForPrintFiltered(String reportNo) {
        List<EntrustMainDTO> allEntrustments = entrustmentMapper.selectForPrint(reportNo);

        // 在Java代码中进行固定条件过滤：第三方类型为"公估"且审批状态为"同意"
        return allEntrustments.stream()
                .filter(e -> "01".equals(e.getThirdPartyType()) && "3".equals(e.getEntrustStatus()))
                .collect(Collectors.toList());
    }
}