package com.paic.ncbs.claim.service.entrustment;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustMainDTO;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustAuditDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO;
import com.paic.ncbs.claim.model.vo.entrustment.EntrustmentApiVo;
import com.paic.ncbs.claim.model.vo.entrustment.ApprovalUserVO;

import java.util.List;
import java.util.Map;

public interface EntrustmentService {

    /**
     * 初始化委托
     * @param entrustmentApiVo 委托信息
     * @throws GlobalBusinessException 业务异常
     */
    ResponseResult<Object> saveEntrustment(EntrustmentApiVo entrustmentApiVo) throws GlobalBusinessException;

    /**
     * 检查是否可以发送委托
     * @throws GlobalBusinessException 业务异常
     */
    void checkIsCanSendEntrustment(EntrustmentApiVo entrustmentApiVo) throws GlobalBusinessException;

    /**
     * 获取事故场景数据
     * @param collectionCode 集合代码
     * @return 事故场景DTO列表
     */
    List<AccidentSceneDto> getAccidentSceneData(String collectionCode);

    /**
     * 获取委托次数
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 委托次数
     */
    Integer getEntrustmentCount(String reportNo, Integer caseTimes);

    /**
     * 获取当前委托
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 委托DTO
     */
    EntrustMainDTO getCurrentEntrustment(String reportNo, Integer caseTimes, String idEntrust);

    /**
     * 获取用于打印的委托信息
     * @return 委托DTO列表
     */
    ResponseResult<Object> getEntrustmentForPrint(String reportNo);

    /**
     * 获取历史委托列表（排除当前案件）
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 历史委托列表
     */
    List<EntrustMainDTO> getHistoryEntrustments(String reportNo, Integer caseTimes, String idEntrustment);

    /**
     * 提交委托审批
     * @param entrustAuditDTO 委托审批信息
     * @throws GlobalBusinessException 业务异常
     */
    void submitentrustAudit(EntrustAuditDTO entrustAuditDTO) throws GlobalBusinessException;

    /**
     * 查询案件机构本级及上级有提调审批权限的所有人
     * @return 审批人信息列表
     * @throws GlobalBusinessException 业务异常
     */
    List<ApprovalUserVO> getApprovalUsers(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    /**
     * 获取当前草稿
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 委托DTO
     */
    EntrustMainDTO getCurrentDraftOrPendingEntrustment(String reportNo, Integer caseTimes);

    /**
     * 根据委托ID查询审批历史轨迹
     * @param idEntrust 委托ID
     * @return 审批历史列表
     */
    List<EntrustAuditDTO> getAuditHistoryByEntrustId(String idEntrust);

    /**
     * 根据报案号和赔付次数查询审批历史轨迹
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 审批历史列表
     */
    List<EntrustAuditDTO> getAuditHistoryByReportNo(String reportNo, Integer caseTimes);

    /**
     * 根据报案号和赔付次数查询第一条委托记录
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 委托DTO
     */
    EntrustMainDTO getFirstEntrustmentByReportNo(String reportNo, Integer caseTimes);
}