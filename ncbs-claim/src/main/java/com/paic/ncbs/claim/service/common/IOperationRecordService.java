package com.paic.ncbs.claim.service.common;

import com.baomidou.mybatisplus.extension.service.IService;
import com.paic.ncbs.claim.dao.entity.common.OperationRecordEntity;
import com.paic.ncbs.claim.model.vo.record.OperationRecordVO;

import java.util.List;


/**
 * <p>
 * 操作记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-23
 */
public interface IOperationRecordService extends IService<OperationRecordEntity> {

    void insertOperationRecord(String reportNo, String operationNode, String description, String remark, String operationUser);

    void insertOperationRecordByLabour(String reportNo, String operationNode, String description, String remark);

    void insertDispatchRecord(String reportNo, String operationNode, Boolean isAutoDispatch, String targetUser, String operationUser);

    void insertDispatchRecordByLabour(String reportNo, String operationNode, Boolean isAutoDispatch, String targetUser);

    List<OperationRecordVO> queryRecord(String reportNo);

}
