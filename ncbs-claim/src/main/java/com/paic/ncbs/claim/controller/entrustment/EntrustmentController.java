package com.paic.ncbs.claim.controller.entrustment;

import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateMapper;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustAuditDTO;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustMainDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO;
import com.paic.ncbs.claim.model.vo.entrustment.ApprovalUserVO;
import com.paic.ncbs.claim.model.vo.entrustment.EntrustmentApiVo;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.service.entrustment.EntrustmentService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Slf4j
@Api(tags = "第三方委托")
@RestController
@RequestMapping("/who/app/entrustmentAction")
public class EntrustmentController extends BaseController {

    @Autowired
    private EntrustmentService entrustmentService;

    @Autowired
    InvestigateMapper investigateMapper;


    @ApiOperation("保存第三方委托")
    @PostMapping("/saveEntrustment")
    public ResponseResult<Object> saveEntrustment(@RequestBody EntrustmentApiVo entrustmentApiVo) {
        return entrustmentService.saveEntrustment(entrustmentApiVo);
    }

    @ApiOperation("查询委托数据")
    @PostMapping("/getEntrustData")
    public ResponseResult<EntrustmentApiVo> getEntrustData(@RequestBody EntrustmentApiVo entrustmentApiVo) {
        log.info("#委托· 查询委托数据#入参#entrustment=" + entrustmentApiVo);
        // 获取报案号和赔付次数
        String reportNo = entrustmentApiVo.getReportNo();
        Integer caseTimes = entrustmentApiVo.getCaseTimes();
        String idEntrust = entrustmentApiVo.getIdEntrust();

        EntrustmentApiVo resultVo = new EntrustmentApiVo();

        EntrustMainDTO entrustMain = null;
        if (StringUtils.hasText(idEntrust)) {
            entrustMain = getEntrustmentService().getCurrentEntrustment(reportNo, caseTimes, idEntrust);
        } else {
            entrustMain = getEntrustmentService().getCurrentDraftOrPendingEntrustment(reportNo, caseTimes);
            if (entrustMain != null) {
                idEntrust = entrustMain.getIdEntrust();
            }
        }
        
        // 设置页面权限判断
        boolean isEditable = determinePagePermission(entrustMain);
        resultVo.setIsEditable(isEditable);
        
        // 获取历史委托数据
        List<EntrustMainDTO> historyEntrustments = entrustmentService.getHistoryEntrustments(reportNo, caseTimes, idEntrust);
        
        String initFlag = entrustmentApiVo.getInitFlag();
        if ("1".equals(initFlag)) {// 录入
            resultVo.setEntrustMainDTO(entrustMain);
            resultVo.setEntrustMainList(historyEntrustments);
            
            // 查询历史审批轨迹 - 改为使用reportNo和caseTimes查询
            List<EntrustAuditDTO> auditHistoryList = entrustmentService.getAuditHistoryByReportNo(reportNo, caseTimes);
            resultVo.setAuditHistoryList(auditHistoryList);
        } else if ("2".equals(initFlag)) {// 审核
            if (entrustMain !=null && StringUtils.hasText(entrustMain.getAccidentCode())){
                List<String> itemNames= Arrays.asList(entrustMain.getAccidentCode().split(","));
                Collections.sort(itemNames);
                String accidentName = investigateMapper.getSelectItemName(itemNames);
                entrustMain.setAccidentName(accidentName);
                for (String e : itemNames) {
                    if (ConstValues.INVESTIGATE_OTHER.contains(e)) {
                        entrustMain.setAccidentName(accidentName + " " + entrustMain.getOther());
                        break;
                    }
                }
            }
            resultVo.setEntrustMainDTO(entrustMain);
            resultVo.setEntrustMainList(historyEntrustments);
        }

        return ResponseResult.success(resultVo);
    }

    /**
     * 判断页面权限
     */
    private boolean determinePagePermission(EntrustMainDTO currentEntrustment) {
        if (currentEntrustment == null) {
            // 没有委托任务，可读写
            return true;
        }
        
        String entrustStatus = currentEntrustment.getEntrustStatus();
        UserInfoDTO currentUser = WebServletContext.getUser();
        String currentUserCode = currentUser != null ? currentUser.getUserCode() : null;
        String processorUserCode = currentEntrustment.getCreatedBy();
        
        switch (entrustStatus) {
            case "0": // 草稿
                return currentUserCode != null && currentUserCode.equals(processorUserCode);
            case "1": // 待审批
                return false;
            case "2": // 不同意
            case "3": // 同意
                return true;
            default:
                return true;
        }
    }

    private EntrustmentService getEntrustmentService() {
        return entrustmentService;
    }

    @ApiOperation("提交委托审批")
    @PostMapping("/submitEntrustAudit")
    public ResponseResult<Object> submitEntrustAudit(@RequestBody EntrustAuditDTO entrustAuditDTO) throws GlobalBusinessException {
        LogUtil.audit("#委托· 完成委托审批#入参#entrustmentAudit=" + entrustAuditDTO);
        entrustmentService.submitentrustAudit(entrustAuditDTO);
        return ResponseResult.success();
    }

    @ApiOperation("查询事故场景")
    @GetMapping(value = "/getAccidentSceneData/{collectionCode}")
    public ResponseResult<List<AccidentSceneDto>> getAccidentSceneData(@PathVariable("collectionCode") String collectionCode) {
        return ResponseResult.success(entrustmentService.getAccidentSceneData(collectionCode));
    }

    @ApiOperation("获取打印委托列表")
    @GetMapping("/getEntrustmentForPrint")
    public ResponseResult<Object> getEntrustmentForPrint(@RequestParam("reportNo") String reportNo) {
        return entrustmentService.getEntrustmentForPrint(reportNo);
    }

    @ApiOperation("获取历史委托列表")
    @GetMapping("/getHistoryEntrustments")
    public ResponseResult<List<EntrustMainDTO>> getHistoryEntrustments(
            @RequestParam("reportNo") String reportNo,
            @RequestParam("caseTimes") Integer caseTimes,
            @RequestParam(value = "idEntrustment", required = false) String idEntrustment) {
        List<EntrustMainDTO> historyEntrustments = entrustmentService.getHistoryEntrustments(reportNo, caseTimes, idEntrustment);
        return ResponseResult.success(historyEntrustments);
    }

    @ApiOperation("获取审批用户列表")
    @GetMapping("/getApprovalUsers")
    public ResponseResult<List<ApprovalUserVO>> getApprovalUsers(
            @RequestParam("reportNo") String reportNo,
            @RequestParam("caseTimes") Integer caseTimes) throws GlobalBusinessException {
        List<ApprovalUserVO> approvalUsers = entrustmentService.getApprovalUsers(reportNo,caseTimes);
        return ResponseResult.success(approvalUsers);
    }

}