package com.paic.ncbs.claim.service.common;


import com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *
 */
public interface ClmsCommonPolicyService {

    public void updatePolicyPays(List<PolicyPayDTO> policyPays);

    void updatePolicyPaysForAdmin(List<PolicyPayDTO> policyPays, String reportNo, Integer caseTimes, String settleAutoSubmit);
}
