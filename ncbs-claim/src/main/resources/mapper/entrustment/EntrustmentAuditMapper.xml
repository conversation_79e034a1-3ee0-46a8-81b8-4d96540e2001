<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentAuditMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.entrustment.EntrustAuditDTO" id="result">
        <id property="idEntrustAudit" column="ID_ENTRUST_AUDIT" />
        <result property="idEntrustMain" column="ID_ENTRUST_MAIN" />
        <result property="reportNo" column="REPORT_NO" />
        <result property="caseTimes" column="CASE_TIMES" />
        <result property="policyNo" column="POLICY_NO" />
        <result property="insuredName" column="INSURED_NAME" />
        <result property="thirdPartyType" column="THIRD_PARTY_TYPE" />
        <result property="entrustDptCode" column="ENTRUST_DPT_CODE" />
        <result property="entrustDptName" column="ENTRUST_DPT_NAME" />
        <result property="submitCode" column="SUBMIT_CODE" />
        <result property="submitName" column="SUBMIT_NAME" />
        <result property="auditorCode" column="AUDITOR_CODE" />
        <result property="auditorName" column="AUDITOR_NAME" />
        <result property="auditorDptCode" column="AUDITOR_DPT_CODE" />
        <result property="auditorDptName" column="AUDITOR_DPT_NAME" />
        <result property="auditOpinion" column="AUDIT_OPINION" />
        <result property="auditTime" column="AUDIT_TIME" />
        <result property="validFlag" column="VALID_FLAG" />
        <result property="remark" column="REMARK" />
        <result property="createdBy" column="CREATED_BY" />
        <result property="sysCtime" column="SYS_CTIME" />
        <result property="updatedBy" column="UPDATED_BY" />
        <result property="sysUtime" column="SYS_UTIME" />
    </resultMap>

    <!-- 插入委托审批记录（条件插入：先检查是否存在待审核记录） -->
    <insert id="insertEntrustmentAudit" parameterType="com.paic.ncbs.claim.model.dto.entrustment.EntrustAuditDTO">
        INSERT INTO CLMS_ENTRUST_AUDIT (
            ID_ENTRUST_AUDIT, ID_ENTRUST_MAIN, REPORT_NO, CASE_TIMES,
            POLICY_NO, INSURED_NAME, THIRD_PARTY_TYPE, ENTRUST_DPT_CODE,
            ENTRUST_DPT_NAME, SUBMIT_CODE, SUBMIT_NAME, AUDITOR_CODE,
            AUDITOR_NAME, AUDITOR_DPT_CODE, AUDITOR_DPT_NAME, AUDIT_OPINION,
            AUDIT_TIME, VALID_FLAG, REMARK,
            CREATED_BY, SYS_CTIME, UPDATED_BY, SYS_UTIME
        )
        SELECT
            #{idEntrustAudit}, #{idEntrustMain}, #{reportNo}, #{caseTimes},
            #{policyNo}, #{insuredName}, #{thirdPartyType}, #{entrustDptCode},
            #{entrustDptName}, #{submitCode}, #{submitName}, #{auditorCode},
            #{auditorName}, #{auditorDptCode}, #{auditorDptName}, #{auditOpinion},
            #{auditTime}, #{validFlag}, #{remark},
            #{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}
        WHERE NOT EXISTS (
            SELECT 1 FROM CLMS_ENTRUST_AUDIT 
            WHERE REPORT_NO = #{reportNo} 
            AND CASE_TIMES = #{caseTimes} 
            AND AUDIT_OPINION = '0'
        )
    </insert>

    <!-- 更新委托审批记录 -->
    <update id="updateEntrustmentAudit" parameterType="com.paic.ncbs.claim.model.dto.entrustment.EntrustAuditDTO">
        UPDATE CLMS_ENTRUST_AUDIT
        <set>
            <if test="idEntrustMain != null">ID_ENTRUST_MAIN = #{idEntrustMain},</if>
            <if test="reportNo != null">REPORT_NO = #{reportNo},</if>
            <if test="caseTimes != null">CASE_TIMES = #{caseTimes},</if>
            <if test="policyNo != null">POLICY_NO = #{policyNo},</if>
            <if test="insuredName != null">INSURED_NAME = #{insuredName},</if>
            <if test="thirdPartyType != null">THIRD_PARTY_TYPE = #{thirdPartyType},</if>
            <if test="entrustDptCode != null">ENTRUST_DPT_CODE = #{entrustDptCode},</if>
            <if test="entrustDptName != null">ENTRUST_DPT_NAME = #{entrustDptName},</if>
            <if test="submitCode != null">SUBMIT_CODE = #{submitCode},</if>
            <if test="submitName != null">SUBMIT_NAME = #{submitName},</if>
            <if test="auditorCode != null">AUDITOR_CODE = #{auditorCode},</if>
            <if test="auditorName != null">AUDITOR_NAME = #{auditorName},</if>
            <if test="auditorDptCode != null">AUDITOR_DPT_CODE = #{auditorDptCode},</if>
            <if test="auditorDptName != null">AUDITOR_DPT_NAME = #{auditorDptName},</if>
            <if test="auditOpinion != null">AUDIT_OPINION = #{auditOpinion},</if>
            <if test="auditTime != null">AUDIT_TIME = #{auditTime},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="remark != null">REMARK = #{remark},</if>
            <if test="updatedBy != null">UPDATED_BY = #{updatedBy},</if>
            <if test="sysUtime != null">SYS_UTIME = #{sysUtime},</if>
        </set>
        WHERE ID_ENTRUST_AUDIT = #{idEntrustAudit}
    </update>

    <!-- 根据主键查询委托审批记录 -->
    <select id="selectById" resultMap="result">
        SELECT * FROM CLMS_ENTRUST_AUDIT
        WHERE ID_ENTRUST_AUDIT = #{idEntrustAudit}
    </select>

    <!-- 根据委托主表ID查询审批记录 -->
    <select id="selectByEntrustmentId" resultMap="result">
        SELECT * FROM CLMS_ENTRUST_AUDIT
        WHERE ID_ENTRUST_MAIN = #{idEntrustMain}
        ORDER BY SYS_CTIME DESC
        LIMIT 1
    </select>

    <!-- 根据报案号查询审批记录 -->
    <select id="selectByReportNo" resultMap="result">
        SELECT * FROM CLMS_ENTRUST_AUDIT
        WHERE REPORT_NO = #{reportNo}
        ORDER BY SYS_CTIME DESC
    </select>

    <!-- 根据审批人查询待审批列表 -->
    <select id="selectPendingAuditList" resultMap="result">
        SELECT * FROM CLMS_ENTRUST_AUDIT
        WHERE AUDITOR_CODE = #{auditorCode}
        AND AUDIT_OPINION IS NULL
        AND VALID_FLAG = 'Y'
        ORDER BY SYS_CTIME DESC
    </select>

    <!-- 根据委托ID查询审批历史轨迹 -->
    <select id="selectAuditHistoryByEntrustId" resultMap="result">
        SELECT * FROM CLMS_ENTRUST_AUDIT
        WHERE ID_ENTRUST_MAIN = #{idEntrust}
        ORDER BY SYS_CTIME DESC
    </select>
    
    <!-- 根据报案号和赔付次数查询审批历史轨迹 -->
    <select id="selectAuditHistoryByReportNo" resultMap="result">
        SELECT * FROM CLMS_ENTRUST_AUDIT
        WHERE REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        ORDER BY SYS_CTIME DESC
    </select>

</mapper>