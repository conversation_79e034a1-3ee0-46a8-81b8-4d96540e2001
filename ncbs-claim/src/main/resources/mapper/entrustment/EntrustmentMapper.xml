<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentMapper">
    
    <resultMap type="com.paic.ncbs.claim.model.dto.entrustment.EntrustMainDTO" id="result">
        <id property="idEntrust" column="ID_ENTRUST" />
        <result property="reportNo" column="REPORT_NO" />
        <result property="caseTimes" column="CASE_TIMES" />
        <result property="thirdPartyType" column="THIRD_PARTY_TYPE" />
        <result property="insuredStatus" column="INSURED_STATUS" />
        <result property="accidentCode" column="ACCIDENT_CODE" />
        <result property="accidentName" column="ACCIDENT_NAME" />
        <result property="other" column="OTHER" />
        <result property="entrustDptCode" column="ENTRUST_DPT_CODE" />
        <result property="entrustDptName" column="ENTRUST_DPT_NAME" />
        <result property="contactName" column="CONTACT_NAME" />
        <result property="contactPhone" column="CONTACT_PHONE" />
        <result property="entrustDes" column="ENTRUST_DES" />
        <result property="entrustName" column="ENTRUST_NAME" />
        <result property="litigationStrategy" column="LITIGATION_STRATEGY" />
        <result property="feeStandard" column="FEE_STANDARD" />
        <result property="auditorCode" column="AUDITOR_CODE" />
        <result property="entrustStatus" column="ENTRUST_STATUS" />
        <result property="fileId" column="FILE_ID" />
        <result property="fileUrl" column="FILE_URL" />
        <result property="printStatus" column="PRINT_STATUS" />
        <result property="optFlag" column="OPT_FLAG" />
        <result property="createdBy" column="CREATED_BY" />
        <result property="sysCtime" column="SYS_CTIME" />
        <result property="updatedBy" column="UPDATED_BY" />
        <result property="sysUtime" column="SYS_UTIME" />
        <result property="validFlag" column="VALID_FLAG" />
    </resultMap>

    <!-- 插入委托信息 -->
    <insert id="insertEntrustment" parameterType="com.paic.ncbs.claim.model.dto.entrustment.EntrustMainDTO">
        INSERT INTO CLMS_ENTRUS_MAIN (
            ID_ENTRUST, REPORT_NO, CASE_TIMES, THIRD_PARTY_TYPE,
            INSURED_STATUS, ACCIDENT_CODE, ACCIDENT_NAME, OTHER,
            ENTRUST_DPT_CODE, ENTRUST_DPT_NAME, CONTACT_NAME, CONTACT_PHONE,
            ENTRUST_DES, ENTRUST_NAME, LITIGATION_STRATEGY, FEE_STANDARD,
            AUDITOR_CODE, ENTRUST_STATUS, FILE_ID, FILE_URL, PRINT_STATUS, OPT_FLAG, VALID_FLAG,
            CREATED_BY, SYS_CTIME, UPDATED_BY, SYS_UTIME
        ) VALUES (
            #{idEntrust}, #{reportNo}, #{caseTimes}, #{thirdPartyType},
            #{insuredStatus}, #{accidentCode}, #{accidentName}, #{other},
            #{entrustDptCode}, #{entrustDptName}, #{contactName}, #{contactPhone},
            #{entrustDes}, #{entrustName}, #{litigationStrategy}, #{feeStandard},
            #{auditorCode}, #{entrustStatus}, #{fileId}, #{fileUrl}, #{printStatus}, #{optFlag}, #{validFlag},
            #{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}
        )
    </insert>

    <!-- 更新委托信息 -->
    <update id="updateEntrustment" parameterType="com.paic.ncbs.claim.model.dto.entrustment.EntrustMainDTO">
        UPDATE CLMS_ENTRUS_MAIN
        <set>
            <if test="reportNo != null">REPORT_NO = #{reportNo},</if>
            <if test="caseTimes != null">CASE_TIMES = #{caseTimes},</if>
            <if test="thirdPartyType != null">THIRD_PARTY_TYPE = #{thirdPartyType},</if>
            <if test="insuredStatus != null">INSURED_STATUS = #{insuredStatus},</if>
            <if test="accidentCode != null">ACCIDENT_CODE = #{accidentCode},</if>
            <if test="accidentName != null">ACCIDENT_NAME = #{accidentName},</if>
            <if test="other != null">OTHER = #{other},</if>
            <if test="entrustDptCode != null">ENTRUST_DPT_CODE = #{entrustDptCode},</if>
            <if test="entrustDptName != null">ENTRUST_DPT_NAME = #{entrustDptName},</if>
            <if test="contactName != null">CONTACT_NAME = #{contactName},</if>
            <if test="contactPhone != null">CONTACT_PHONE = #{contactPhone},</if>
            <if test="entrustDes != null">ENTRUST_DES = #{entrustDes},</if>
            <if test="entrustName != null">ENTRUST_NAME = #{entrustName},</if>
            <if test="litigationStrategy != null">LITIGATION_STRATEGY = #{litigationStrategy},</if>
            <if test="feeStandard != null">FEE_STANDARD = #{feeStandard},</if>
            <if test="auditorCode != null">AUDITOR_CODE = #{auditorCode},</if>
            <if test="entrustStatus != null">ENTRUST_STATUS = #{entrustStatus},</if>
            <if test="fileId != null">FILE_ID = #{fileId},</if>
            <if test="fileUrl != null">FILE_URL = #{fileUrl},</if>
            <if test="printStatus != null">PRINT_STATUS = #{printStatus},</if>
            <if test="optFlag != null">OPT_FLAG = #{optFlag},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="updatedBy != null">UPDATED_BY = #{updatedBy},</if>
            <if test="sysUtime != null">SYS_UTIME = #{sysUtime},</if>
        </set>
        WHERE ID_ENTRUST = #{idEntrust}
    </update>

    <!-- 删除草稿任务 -->
    <delete id="deleteEntrustmentNoOperate">
        DELETE FROM CLMS_ENTRUS_MAIN
        WHERE REPORT_NO = #{reportNo}
          AND CASE_TIMES = #{caseTimes}
          AND ID_ENTRUST  = #{idEntrustment}
          AND ENTRUST_STATUS = '0'
    </delete>

    <!-- 根据报案号和赔付次数查询 -->
    <select id="selectByReportNoAndCaseTime" resultMap="result">
        SELECT * FROM CLMS_ENTRUS_MAIN
        WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        <if test="idEntrust != null and idEntrust != '' ">
            and ID_ENTRUST  = #{idEntrust}
        </if>
        ORDER BY SYS_CTIME DESC
        LIMIT 1
    </select>

    <!-- 根据报案号查询历史记录 -->
    <select id="selectHistoryByReportNo" resultMap="result">
        SELECT * FROM CLMS_ENTRUS_MAIN
        WHERE REPORT_NO = #{reportNo}
        ORDER BY SYS_CTIME DESC
    </select>

    <!-- 根据审批人查询待审批列表 -->
    <select id="selectPendingApprovalList" resultMap="result">
        SELECT * FROM CLMS_ENTRUS_MAIN
        WHERE AUDITOR_UM_CODE = #{approverUmCode}
        AND ENTRUST_STATUS = '1'
        ORDER BY SYS_CTIME DESC
    </select>

    <!-- 根据主键查询 -->
    <select id="selectById" resultMap="result">
        SELECT * FROM CLMS_ENTRUS_MAIN
        WHERE ID_ENTRUST = #{idEntrust}
    </select>

    <!-- 查询用于打印的委托信息 -->
    <select id="selectForPrint" resultMap="result">
        SELECT * FROM CLMS_ENTRUS_MAIN
        WHERE REPORT_NO = #{reportNo}
        AND THIRD_PARTY_TYPE = '01'
        AND ENTRUST_STATUS = '3'
        ORDER BY SYS_CTIME DESC
    </select>

    <!-- 查询未审批完成的第三方委托任务 -->
    <select id="selectUnapprovedEntrustments" resultType="com.paic.ncbs.claim.model.dto.entrustment.EntrustMainDTO">
        SELECT * FROM CLMS_ENTRUS_MAIN
        WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        AND ENTRUST_STATUS IN ('0', '1')
        AND VALID_FLAG = 'Y'
    </select>
    
    <!-- 查询未审批完成的委托任务 -->
    <select id="getUnapprovedEntrustments" resultMap="result">
        SELECT * FROM CLMS_ENTRUS_MAIN
        WHERE REPORT_NO = #{reportNo} 
        AND CASE_TIMES = #{caseTimes}
        AND ENTRUST_STATUS IN ('0', '1')
        AND VALID_FLAG = 'Y'
    </select>
    
    <!-- 获取未提交的委托数据 -->
    <select id="getNoCommitData" resultMap="result">
        SELECT * FROM CLMS_ENTRUS_MAIN
        WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        AND ENTRUST_STATUS = '0'
        AND VALID_FLAG = 'Y'
        ORDER BY SYS_CTIME DESC
        LIMIT 1
    </select>

    <!-- 根据报案号查询保单号和被保险人姓名 -->
    <select id="getPolicyInfoByReportNo" resultType="java.util.HashMap">
        SELECT
            pi.POLICY_NO as policyNo,
            ip.NAME as insuredName
        FROM CLMS_POLICY_INFO pi
        LEFT JOIN CLMS_INSURED_PERSON ip ON pi.ID_AHCS_POLICY_INFO = ip.ID_AHCS_POLICY_INFO
        WHERE pi.REPORT_NO = #{reportNo}
        LIMIT 1
    </select>

    <!-- 根据报案号和赔付次数查询案件机构代码 -->
    <select id="getDepartmentCodeByReportNo" resultType="java.lang.String">
        SELECT P.DEPARTMENT_CODE
        FROM CLMS_POLICY_INFO P
        WHERE P.REPORT_NO = TRIM(#{reportNo})
        LIMIT 1
    </select>

    <!-- 根据报案号和赔付次数查询第一条委托记录 -->
    <select id="selectFirstByReportNoAndCaseTimes" resultMap="result">
        SELECT * FROM CLMS_ENTRUS_MAIN
        WHERE REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        ORDER BY SYS_CTIME
        LIMIT 1
    </select>

</mapper>