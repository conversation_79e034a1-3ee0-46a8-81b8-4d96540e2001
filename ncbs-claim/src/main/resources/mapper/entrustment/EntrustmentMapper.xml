<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentMapper">
    
    <resultMap type="com.paic.ncbs.claim.model.dto.entrustment.EntrustMainDTO" id="result">
        <id property="idEntrust" column="ID_ENTRUST" />
        <result property="reportNo" column="REPORT_NO" />
        <result property="caseTimes" column="CASE_TIMES" />
        <result property="thirdPartyType" column="THIRD_PARTY_TYPE" />
        <result property="insuredStatus" column="INSURED_STATUS" />
        <result property="accidentCode" column="ACCIDENT_CODE" />
        <result property="accidentName" column="ACCIDENT_NAME" />
        <result property="other" column="OTHER" />
        <result property="entrustDptCode" column="ENTRUST_DPT_CODE" />
        <result property="entrustDptName" column="ENTRUST_DPT_NAME" />
        <result property="contactName" column="CONTACT_NAME" />
        <result property="contactPhone" column="CONTACT_PHONE" />
        <result property="entrustDes" column="ENTRUST_DES" />
        <result property="entrustName" column="ENTRUST_NAME" />
        <result property="litigationStrategy" column="LITIGATION_STRATEGY" />
        <result property="feeStandard" column="FEE_STANDARD" />
        <result property="auditorCode" column="AUDITOR_CODE" />
        <result property="entrustStatus" column="ENTRUST_STATUS" />
        <result property="fileId" column="FILE_ID" />
        <result property="fileUrl" column="FILE_URL" />
        <result property="printStatus" column="PRINT_STATUS" />
        <result property="optFlag" column="OPT_FLAG" />
        <result property="createdBy" column="CREATED_BY" />
        <result property="sysCtime" column="SYS_CTIME" />
        <result property="updatedBy" column="UPDATED_BY" />
        <result property="sysUtime" column="SYS_UTIME" />
        <result property="validFlag" column="VALID_FLAG" />
    </resultMap>

    <!-- 插入委托信息 -->
    <insert id="insertEntrustment" parameterType="com.paic.ncbs.claim.model.dto.entrustment.EntrustMainDTO">
        INSERT INTO clms_entrust_main (
            id_entrust, report_no, case_times, third_party_type,
            insured_status, accident_code, other,
            entrust_dpt_code, entrust_dpt_name, contact_name, contact_phone,
            entrust_des, entrust_name, litigation_strategy, fee_standard,
            auditor_code, entrust_status, file_id, print_status, valid_flag,
            created_by, sys_ctime, updated_by, sys_utime
        ) VALUES (
            #{idEntrust}, #{reportNo}, #{caseTimes}, #{thirdPartyType},
            #{insuredStatus}, #{accidentCode}, #{other},
            #{entrustDptCode}, #{entrustDptName}, #{contactName}, #{contactPhone},
            #{entrustDes}, #{entrustName}, #{litigationStrategy}, #{feeStandard},
            #{auditorCode}, #{entrustStatus}, #{fileId}, #{printStatus}, #{validFlag},
            #{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}
        )
    </insert>

    <!-- 更新委托信息 -->
    <update id="updateEntrustment" parameterType="com.paic.ncbs.claim.model.dto.entrustment.EntrustMainDTO">
        UPDATE clms_entrust_main
        <set>
            <if test="reportNo != null">report_no = #{reportNo},</if>
            <if test="caseTimes != null">case_times = #{caseTimes},</if>
            <if test="thirdPartyType != null">third_party_type = #{thirdPartyType},</if>
            <if test="insuredStatus != null">insured_status = #{insuredStatus},</if>
            <if test="accidentCode != null">accident_code = #{accidentCode},</if>
            <if test="other != null">other = #{other},</if>
            <if test="entrustDptCode != null">entrust_dpt_code = #{entrustDptCode},</if>
            <if test="entrustDptName != null">entrust_dpt_name = #{entrustDptName},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="entrustDes != null">entrust_des = #{entrustDes},</if>
            <if test="entrustName != null">entrust_name = #{entrustName},</if>
            <if test="litigationStrategy != null">litigation_strategy = #{litigationStrategy},</if>
            <if test="feeStandard != null">fee_standard = #{feeStandard},</if>
            <if test="auditorCode != null">auditor_code = #{auditorCode},</if>
            <if test="entrustStatus != null">entrust_status = #{entrustStatus},</if>
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="printStatus != null">print_status = #{printStatus},</if>
            <if test="validFlag != null">valid_flag = #{validFlag},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime},</if>
        </set>
        WHERE id_entrust = #{idEntrust}
    </update>

    <!-- 删除草稿任务 -->
    <delete id="deleteEntrustmentNoOperate">
        DELETE FROM CLMS_ENTRUS_MAIN
        WHERE REPORT_NO = #{reportNo}
          AND CASE_TIMES = #{caseTimes}
          AND ID_ENTRUST  = #{idEntrust}
          AND ENTRUST_STATUS = '0'
    </delete>

    <!-- 通用条件查询委托记录 -->
    <select id="selectByConditions" resultMap="result">
        SELECT * FROM CLMS_ENTRUS_MAIN
        WHERE REPORT_NO = #{reportNo}
        <if test="caseTimes != null">
            AND CASE_TIMES = #{caseTimes}
        </if>
        <if test="idEntrust != null and idEntrust != ''">
            AND ID_ENTRUST = #{idEntrust}
        </if>
        <if test="entrustStatus != null and entrustStatus != ''">
            AND ENTRUST_STATUS IN
            <foreach collection="entrustStatus.split(',')" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        AND VALID_FLAG = 'Y'
        <choose>
            <when test="orderBy == 'ASC'">
                ORDER BY SYS_CTIME ASC
            </when>
            <otherwise>
                ORDER BY SYS_CTIME DESC
            </otherwise>
        </choose>
        <if test="limitOne != null and limitOne == true">
            LIMIT 1
        </if>
    </select>

    <!-- 根据报案号查询历史记录 -->
    <select id="selectHistoryByReportNo" resultMap="result">
        SELECT * FROM CLMS_ENTRUS_MAIN
        WHERE REPORT_NO = #{reportNo}
        ORDER BY SYS_CTIME DESC
    </select>

    <!-- 根据主键查询 -->
    <select id="selectById" resultMap="result">
        SELECT * FROM CLMS_ENTRUS_MAIN
        WHERE ID_ENTRUST = #{idEntrust}
    </select>

    <!-- 查询用于打印的委托信息（移除固定条件到Java代码中处理） -->
    <select id="selectForPrint" resultMap="result">
        SELECT * FROM CLMS_ENTRUS_MAIN
        WHERE REPORT_NO = #{reportNo}
        AND VALID_FLAG = 'Y'
        ORDER BY SYS_CTIME DESC
    </select>

    <!-- 根据报案号查询保单号和被保险人姓名 -->
    <select id="getPolicyInfoByReportNo" resultType="java.util.HashMap">
        SELECT
            pi.POLICY_NO as policyNo,
            ip.NAME as insuredName
        FROM CLMS_POLICY_INFO pi
        LEFT JOIN CLMS_INSURED_PERSON ip ON pi.ID_AHCS_POLICY_INFO = ip.ID_AHCS_POLICY_INFO
        WHERE pi.REPORT_NO = #{reportNo}
        LIMIT 1
    </select>

    <!-- 根据报案号查询案件机构代码 -->
    <select id="getDepartmentCodeByReportNo" resultType="java.lang.String">
        SELECT P.DEPARTMENT_CODE
        FROM CLMS_POLICY_INFO P
        WHERE P.REPORT_NO = TRIM(#{reportNo})
        LIMIT 1
    </select>

</mapper>