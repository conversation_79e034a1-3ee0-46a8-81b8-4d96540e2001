package com.paic.ncbs.claim.controller;

import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.mock.web.MockServletContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.doc.PrintController;
import com.paic.ncbs.claim.controller.entrustment.EntrustmentController;
import com.paic.ncbs.claim.controller.report.TaskListController;
import com.paic.ncbs.claim.dao.entity.report.ActivitiTaskUrlCfg;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustMainDTO;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustAuditDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO;
import com.paic.ncbs.claim.model.dto.print.PrintEntrustDTO;
import com.paic.ncbs.claim.model.vo.entrustment.ApprovalUserVO;
import com.paic.ncbs.claim.model.vo.entrustment.EntrustmentApiVo;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO;
import com.paic.ncbs.claim.service.entrustment.EntrustmentService;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.file.service.FileCommonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.servlet.ServletContext;

@SpringBootTest
public class EntrustmentAndPrintControllerTest {

    @Autowired
    private EntrustmentController entrustmentController;

    @Autowired
    private PrintController printController;
    
    @Autowired
    private TaskListController taskListController;

    @Autowired
    private EntrustmentService entrustmentService;

    private EntrustmentApiVo entrustmentApiVo;

    private PrintEntrustDTO printEntrustDTO;

    @Autowired
    private FileCommonService fileCommonService;
    @Autowired
    private InvestigateService investigateService;


    @Test
    @DisplayName("保存提交")
    public void testAddEntrustment() {
//        EntrustmentApiVo entrust = entrustmentApiVo;
//        entrust.setReportNo("97851000000002008961");
//        entrust.setCaseTimes(1);
//        entrust.setSubmitFlag("1"); // 1-提交 0-保存
//
//        entrust.getEntrustMainDTO().setIdEntrust("2b046c256b0e4f238f40866e3ed97b36");
//        entrust.getEntrustMainDTO().setOptFlag(entrust.getSubmitFlag()); // 操作状态 0：暂存 1提交
//        entrust.getEntrustMainDTO().setEntrustStatus(entrust.getSubmitFlag()); // 0-草稿、1-待审批
//        entrust.getEntrustMainDTO().setAuditorCode("blueshen"); // 审批人
//        entrust.getEntrustMainDTO().setAuditorUmName("沈文"); // 审批人名称
//        entrust.getEntrustMainDTO().setAuditorDpmCode("1"); // 审批人机构

        String jsonStirng = "{\"caseTimes\":\"1\",\"reportNo\":\"98081000000002009076\",\"submitFlag\":\"0\",\"entrustMainDTO\":{\"accidentCode\":[],\"entrustName\":\"\",\"contactPhone\":\"\",\"contactName\":\"\",\"litigationStrategy\":\"\",\"feeStandard\":\"\",\"entrustDes\":\"\",\"reportNo\":\"98081000000002009076\",\"caseTimes\":\"1\",\"userCode\":\"\",\"userName\":\"\",\"comName\":\"\",\"comCode\":\"\"}}";
        EntrustmentApiVo entrust = JSON.parseObject(jsonStirng, EntrustmentApiVo.class);

        entrustmentController.saveEntrustment(entrust);
    }

    @Test
    @DisplayName("查询")
    public void testGetEntrustmentData() {
        EntrustmentApiVo entrust = new EntrustmentApiVo();
        entrust.setReportNo("98080000000001004259");
        entrust.setCaseTimes(1);
        entrust.setInitFlag("2");
//        entrust.setIdEntrust("410b6947148c45df8d5ca5e91ca0ec26");
                assertDoesNotThrow(() -> {
            ResponseResult<EntrustmentApiVo> result =  entrustmentController.getEntrustData(entrust);
            assertNotNull(result);
            // 验证返回结果
            assertNotNull(result.getCode());
        });
    }

    @Test
    @DisplayName("事故场景查询")
    public void testGetAccidentSceneData() {
        assertDoesNotThrow(() -> {
            ResponseResult<List<AccidentSceneDto>> result = entrustmentController.getAccidentSceneData("IS_0101");
            assertNotNull(result);
            assertNotNull(result.getCode());
        });
    }

    @Test
    @DisplayName("审核提交 - 同意")
    public void testSubmitEntrustAuditAgree() {

        // 创建测试用的EntrustAuditDTO对象
        EntrustAuditDTO entrustAuditDTO = new EntrustAuditDTO();
        entrustAuditDTO.setIdEntrustMain("2b046c256b0e4f238f40866e3ed97b36"); // 需要一个有效的委托主键
        entrustAuditDTO.setIdEntrustAudit("27bdc090107b40c9aabaaf39a488e2c6");// 审核主键
        entrustAuditDTO.setRemark("审核意见XXXXXXXXXXXXXXXXXXXXXX");//
        entrustAuditDTO.setAuditOpinion("3"); // 3-同意
        
        // 直接调用service方法进行测试
        assertDoesNotThrow(() -> {
            entrustmentService.submitentrustAudit(entrustAuditDTO);
        });
    }
    
    @Test
    @DisplayName("审核提交 - 不同意")
    public void testSubmitEntrustAuditDisagree() {
//        {"auditOpinion":"2","remark":"1","idEntrustAudit":"27bdc090107b40c9aabaaf39a488e2c6","idEntrustMain":"","initFlag":2}
        // 创建测试用的EntrustAuditDTO对象
        EntrustAuditDTO entrustAuditDTO = new EntrustAuditDTO();
//        entrustAuditDTO.setIdEntrustMain("2b046c256b0e4f238f40866e3ed97b36"); // 需要一个有效的委托主键
        entrustAuditDTO.setIdEntrustAudit("27bdc090107b40c9aabaaf39a488e2c6");// 审核主键
        entrustAuditDTO.setRemark("审核意见XXXXXXXXXXXXXXXXXXXXXX");//
        entrustAuditDTO.setAuditOpinion("2"); // 2-不同意
        
        // 直接调用service方法进行测试
        assertDoesNotThrow(() -> {
            entrustmentService.submitentrustAudit(entrustAuditDTO);
        });
    }

    @Test
    @DisplayName("PDF打印 - 提调场景")
    public void testCommissionPrintResultForInvestigate() {
        // 设置为提调场景
        printEntrustDTO.setIdAhcsInvestigate("2b046c256b0e4f238f40866e3ed97b22");
        printEntrustDTO.setPrintFlag("1");
        
        // 测试提调场景下的公估委托书下载
        assertDoesNotThrow(() -> {
            ResponseResult<Object> result = printController.commissionPrintResult(printEntrustDTO);
            assertNotNull(result);
            // 验证返回结果
            assertNotNull(result.getCode());
        });
    }
    @Test
    @DisplayName("PDF打印 - 委托")
    public void testCommissionPrintResultForCommission() {
        // 设置为委托场景
        printEntrustDTO.setIdEntrust("5b223ea8834b402eb84bb45389ab10c8");
        printEntrustDTO.setPrintFlag("2");
        
        // 测试委托场景下的公估委托书下载
        assertDoesNotThrow(() -> {
            ResponseResult<Object> result = printController.commissionPrintResult(printEntrustDTO);
            assertNotNull(result);
            // 验证返回结果
            assertNotNull(result.getCode());
        });
    }
    @Test
    @DisplayName("打印文件获取")
    public void testGetEntrustmentForPrint() throws NcbsException {

        assertDoesNotThrow(() -> {
            String result = fileCommonService.getDownloadUrl("https://dsuprivate-1313541860.cos.ap-shanghai-fsi.myqcloud.com/ncbs-claim/1955226843162657390-fcccbf9fa7834ea59c773e28c1704d8b.pdf",null,"沈文");
            assertNotNull(result);
        });
    }

    @Test
    @DisplayName("打印 - 委托列表查询")
    public void testGetEntrustmentForPrintList() {
        // 直接调用controller方法进行测试
        assertDoesNotThrow(() -> {
            ResponseResult<Object> result = entrustmentController.getEntrustmentForPrint("98081000000002009293");
            assertNotNull(result);
            // 验证返回结果
            assertNotNull(result.getCode());
        });
    }

    @Test
    @DisplayName("测试获取用户任务列表接口")
    public void testGetUserTaskListNew() throws Exception {
        // 直接调用controller方法
        ActivitiTaskUrlCfg activitiTaskUrlCfg = new ActivitiTaskUrlCfg();
        activitiTaskUrlCfg.setTaskDefinitionBpmCode("OC_ENTRUSTMENT_APPROVAL");
        
        // 调用方法并获取结果
        ResponseResult<List<WorkBenchTaskVO>> result = taskListController.getUserTaskListNew(activitiTaskUrlCfg);
        
        // 验证返回结果
        assertNotNull(result);
        assertNotNull(result.getCode());
        System.out.println("返回结果：" + result);
        
        // 验证数据存在
        if (result.getData() != null) {
            System.out.println("数据条数：" + result.getData().size());
            // 遍历并打印部分数据
            result.getData().stream().limit(5).forEach(task -> {
                System.out.println("任务: " + task.getReportNo() + ", " + task.getTaskDefinitionBpmKey());
            });
        }
    }

    @Test
    @DisplayName("审批列表")
    public void testGetApprovalUsers() {
        assertDoesNotThrow(() -> {
            ResponseResult<List<ApprovalUserVO>> result = entrustmentController.getApprovalUsers("98080000000002009070",1);
            assertNotNull(result);
            assertNotNull(result.getCode());
            
            // 如果有数据，验证数据结构
            if (result.getData() != null && !result.getData().isEmpty()) {
                ApprovalUserVO firstUser = result.getData().get(0);
                assertNotNull(firstUser.getUserCode());
                assertNotNull(firstUser.getUserName());
                System.out.println("第一个审批用户: " + firstUser.getUserName() + "(" + firstUser.getUserCode() + ")");
            }
        });
    }


    @Test
    @DisplayName("公估公司")
    public void testGetApprovalUsers1() {
        assertDoesNotThrow(() -> {
            ResponseResult<Object> result = investigateService.getServerInfoList();
            System.out.println("返回结果：" + result);
            assertNotNull(result);
            assertNotNull(result.getCode());
        });
    }


    private static final Logger log = LoggerFactory.getLogger(EntrustmentAndPrintControllerTest.class);

    @Autowired
    private WebApplicationContext webApplicationContext;
    private ServletContext servletContext;
    private MockHttpServletRequest mockRequest;
    private MockHttpServletResponse mockResponse;
    private MockHttpSession mockSession;


    /**
     * 初始化WebServletContext并设置所有参数
     */
    private void initializeWebServletContext() {
        // 获取ServletContext
        servletContext = webApplicationContext.getServletContext();

        if (servletContext == null) {
            // 如果没有ServletContext，创建MockServletContext
            servletContext = new MockServletContext();
        }

        // 设置应用基本信息
        servletContext.setAttribute("applicationName", "ncbs-claim");
        servletContext.setAttribute("applicationVersion", "1.0.0");
        servletContext.setAttribute("environment", "test");

        // 设置数据库连接信息
        servletContext.setAttribute("datasource.url", "*******************************************");
        servletContext.setAttribute("datasource.username", "test_user");
        servletContext.setAttribute("datasource.driver", "com.mysql.cj.jdbc.Driver");

        // 设置Redis连接信息
        servletContext.setAttribute("redis.host", "localhost");
        servletContext.setAttribute("redis.port", "6379");
        servletContext.setAttribute("redis.database", "0");
        servletContext.setAttribute("redis.timeout", "3000");

        // 设置文件上传配置
        servletContext.setAttribute("file.upload.path", "/tmp/ncbs-claim/uploads");
        servletContext.setAttribute("file.upload.maxSize", "10485760"); // 10MB
        servletContext.setAttribute("file.upload.allowedTypes", "pdf,jpg,jpeg,png,doc,docx,xls,xlsx");

        // 设置业务配置参数
        servletContext.setAttribute("business.replevy.maxAmount", "1000000.00");
        servletContext.setAttribute("business.replevy.timeoutDays", "30");
        servletContext.setAttribute("business.replevy.approveLevel", "3");
        servletContext.setAttribute("business.replevy.autoApproveAmount", "50000.00");

        // 设置系统配置参数
        servletContext.setAttribute("system.pageSize", "20");
        servletContext.setAttribute("system.sessionTimeout", "1800"); // 30分钟
        servletContext.setAttribute("system.logLevel", "INFO");
        servletContext.setAttribute("system.enableCache", "true");

        // 设置安全配置参数
        servletContext.setAttribute("security.tokenExpireTime", "7200"); // 2小时
        servletContext.setAttribute("security.maxLoginAttempts", "5");
        servletContext.setAttribute("security.passwordMinLength", "8");
        servletContext.setAttribute("security.enableEncryption", "true");

        // 设置邮件配置参数
        servletContext.setAttribute("mail.smtp.host", "smtp.test.com");
        servletContext.setAttribute("mail.smtp.port", "587");
        servletContext.setAttribute("mail.smtp.username", "<EMAIL>");
        servletContext.setAttribute("mail.smtp.enableTLS", "true");

        // 设置消息队列配置
        servletContext.setAttribute("mq.host", "localhost");
        servletContext.setAttribute("mq.port", "5672");
        servletContext.setAttribute("mq.username", "guest");
        servletContext.setAttribute("mq.virtualHost", "/");

        // 设置第三方接口配置
        servletContext.setAttribute("api.payment.url", "https://api.test.com/payment");
        servletContext.setAttribute("api.payment.timeout", "30000");
        servletContext.setAttribute("api.payment.retryTimes", "3");
        servletContext.setAttribute("api.payment.appKey", "test_app_key");

        // 设置监控配置
        servletContext.setAttribute("monitor.enable", "true");
        servletContext.setAttribute("monitor.interval", "60"); // 60秒
        servletContext.setAttribute("monitor.alertThreshold", "80");

        // 设置缓存配置
        servletContext.setAttribute("cache.enable", "true");
        servletContext.setAttribute("cache.expireTime", "3600"); // 1小时
        servletContext.setAttribute("cache.maxSize", "1000");

        // 设置日志配置
        servletContext.setAttribute("log.path", "/var/log/ncbs-claim");
        servletContext.setAttribute("log.maxFileSize", "100MB");
        servletContext.setAttribute("log.maxHistory", "30");
        servletContext.setAttribute("log.pattern", "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n");

        // 设置测试专用参数
        servletContext.setAttribute("test.mode", "true");
        servletContext.setAttribute("test.mockData", "true");
        servletContext.setAttribute("test.skipValidation", "false");
        servletContext.setAttribute("test.debugMode", "true");

        // 设置追偿业务专用参数
        servletContext.setAttribute("replevy.workflow.enable", "true");
        servletContext.setAttribute("replevy.notification.enable", "true");
        servletContext.setAttribute("replevy.autoAssign.enable", "true");
        servletContext.setAttribute("replevy.document.required", "true");

        log.info("WebServletContext初始化完成，已设置所有参数");
    }

    /**
     * 初始化Mock对象和用户Session
     */
    private void initializeMockRequestAndUserSession() {
        // 创建Mock对象
        mockRequest = new MockHttpServletRequest();
        mockResponse = new MockHttpServletResponse();
        mockSession = new MockHttpSession();

        // 设置Session到Request
        mockRequest.setSession(mockSession);

        // 创建测试用户对象
        com.paic.ncbs.um.model.dto.UserInfoDTO userInfoDTO = createTestUserInfoDTO();

        // 将用户对象设置到Session中
        mockSession.setAttribute(Constants.CURR_USER, userInfoDTO);
        mockSession.setAttribute(Constants.CURR_COMCODE, userInfoDTO.getComCode());

        // 设置RequestContextHolder，使得静态方法能够获取到Request
        ServletRequestAttributes attributes = new ServletRequestAttributes(mockRequest, mockResponse);
        RequestContextHolder.setRequestAttributes(attributes);

        log.info("Mock Request和用户Session初始化完成，用户: {}, 机构: {}",
                userInfoDTO.getUserName(), userInfoDTO.getComCode());
    }

    /**
     * 创建测试用的UserInfoDTO对象
     * @return UserInfoDTO测试对象
     */
    private com.paic.ncbs.um.model.dto.UserInfoDTO createTestUserInfoDTO() {
        com.paic.ncbs.um.model.dto.UserInfoDTO userInfoDTO = new com.paic.ncbs.um.model.dto.UserInfoDTO();

        // 基本用户信息
        userInfoDTO.setUserCode("blueshen");           // 用户代码
        userInfoDTO.setUserName("测试用户沈文");             // 用户姓名
        userInfoDTO.setComCode("1001");                     // 机构代码
        userInfoDTO.setComName("测试机构");                 // 机构名称

        // 联系信息
        userInfoDTO.setEmail("<EMAIL>");         // 邮箱
        userInfoDTO.setMobile("13800138001");               // 手机号

//        log.debug("创建测试用户对象: {}", userInfoDTO.getUserName());
        return userInfoDTO;
    }


    @BeforeEach
    public void setUp() {
        // 初始化WebServletContext并设置参数
        initializeWebServletContext();

        // 初始化Mock对象和用户Session
        initializeMockRequestAndUserSession();

        // 初始化委托相关测试数据
        entrustmentApiVo = new EntrustmentApiVo();
        entrustmentApiVo.setReportNo("R000000001");
        entrustmentApiVo.setCaseTimes(1);
        entrustmentApiVo.setSubmitFlag("1");
        entrustmentApiVo.setInitFlag("1"); // 1代表录入页面初始化

        // 初始化委托DTO对象
        EntrustMainDTO entrustmentDTO = new EntrustMainDTO();
        entrustmentDTO.setReportNo("90010000000001005288");
        entrustmentDTO.setCaseTimes(1);
        entrustmentDTO.setThirdPartyType("01"); // 第三方类型：01-公估
        entrustmentDTO.setInsuredStatus("IS_0101");
        entrustmentDTO.setAccidentCode("ASM_2001,ASM_2002,ASM_2003,ASM_2004,ASM_2005,ASM_2006,ASM_2007");
        entrustmentDTO.setAccidentName("交通事故");
        entrustmentDTO.setEntrustDptCode("DPT001");
        entrustmentDTO.setEntrustDptName("公估公司A");
        entrustmentDTO.setContactName("张三");
        entrustmentDTO.setContactPhone("13800138000");
        entrustmentDTO.setEntrustDes("详细说明委托事项");
        entrustmentDTO.setEntrustName("委托对象");
        entrustmentDTO.setLitigationStrategy("诉讼策略");
        entrustmentDTO.setFeeStandard("收费标准");
        entrustmentDTO.setAuditorDpmCode("1");// 处理机构代码
        entrustmentDTO.setAuditorDpmName("处理机构名称");
        entrustmentDTO.setOptFlag("1"); // 1-提交
        entrustmentDTO.setEntrustStatus("1"); // 1-待审批
        entrustmentDTO.setAccidentCode("blueshen");
        entrustmentApiVo.setEntrustMainDTO(entrustmentDTO);

        // 初始化打印相关测试数据
        printEntrustDTO = new PrintEntrustDTO();
        printEntrustDTO.setReportNo("90010000000001005288");
        printEntrustDTO.setPolicyNo("P000000001");
        // printFlag 1-提调 2-委托
        printEntrustDTO.setPrintFlag("1"); // 提调场景
    }
}